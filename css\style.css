:root{
    --main-background:#191a1a;
    --primary-line-color:#ff2770;
    --primary-line2-color:#fff;
    --secondary-text-color:#fff;
    --secondary-text2-color:#000;
    --shadow-color: #fff;

}
@import url(font/Almarai.css);
* {
    font-family: <PERSON><PERSON>, sans-serif, <PERSON><PERSON>;
    margin:0; padding:0;
    box-sizing: border-box;
    outline: none; border:none;
    text-decoration: none;
    scroll-behavior: smooth;
    transition:all .3s cubic-bezier(.38,1.15,.7,1.12);
  }
  *::selection{
    background-color: var(--main-background);
    color:#fff;
}
html::-webkit-scrollbar{
    width:0.5rem;
}
html::-webkit-scrollbar-track{
    background-color: var(--primary-line2-color);
}
html::-webkit-scrollbar-thumb{
    background-color: var(--primary-line-color);
}
body{
    min-height: calc(100vh);
    padding-top: 20px;
    padding-bottom: 20px;
    background:var(--main-background);
    color: #fff;
}
body.dark-theme{
    --main-background:#eee;
    --primary-line-color:#ff2770;
    --primary-line2-color:#191a1a;
    --secondary-text-color:#191a1a;
    --secondary-text2-color:#fff;
    --shadow-color: #505050;
}
.box{
    position: relative;
    width: 360px;
    height: 710px;
    margin: 50px auto auto auto;
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.screen-background-r{
    width: 300px; 
    margin: 10px auto 50px  auto;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    direction: rtl;
    color: var(--secondary-text-color);
}
.screen-background-r h5{
    margin-bottom: 10px;
}
.screen-background-r h6{
    font-size: 12px;
}
.border-right{
    position: absolute;
    border: 4px solid var(--primary-line2-color);
    width: 150px;
    height: 150px;
    background: none;
    border-radius: 30px 20px;
    top: 2px;
    left: 5px;
    z-index: -1;
}
.border-left{
    position: absolute;
    border: 4px solid var(--primary-line2-color);
    width: 150px;
    height: 150px;
    border-radius: 30px 20px;
    bottom: 2px;
    right: 5px;
    z-index: -1;
}
.border-left-top{
    position: absolute;
    border: 4px solid var(--primary-line-color);
    width: 150px;
    height: 150px;
    border-radius: 20px 30px ;
    top: 2px;
    right: 5px;
    z-index: -1;
}
.border-right-bottom{
    position: absolute;
    border: 4px solid var(--primary-line-color);
    width: 150px;
    height: 150px;
    border-radius: 20px 30px;
    bottom: 2px;
    left: 5px;
    z-index: -1;
}
.border-right-right{
    position: absolute;
    border: 4px solid #ff5500;
    width: 150px;
    height: 300px;
    border-radius: 20px;
    left: 5px;
    z-index: -1;
}
.border-left-left{
    position: absolute;
    border: 4px solid #ff5500;
    width: 150px;
    height: 300px;
    border-radius: 20px;
    right: 5px;
    z-index: -1;
}
.scrols{
 width: 100%;
 padding: 20px;
 overflow: auto;
}
.scrols::-webkit-scrollbar{
    width:0.5rem;
}
.scrols::-webkit-scrollbar-track{
    background-color: var(--primary-line2-color);
}
.scrols::-webkit-scrollbar-thumb{
    background-color: var(--primary-line-color);
}
.login{
    position: relative;
    width: 100%;
    height: 100%;
    padding: 20px;
    display: flex;
    align-items: center;
    border-radius: 30px;
    flex-direction: column;
    overflow: hidden;
}
.outscreen{
    position: relative;
    width: 90%;
    height: 95%;
    border-radius: 30px;
    background:var(--main-background);
    box-shadow: 0 0 15px var(--shadow-color);
    overflow: hidden;
}
.login.nav-toggle{
    display: none;
    transform: scale(1);
}
.prices{
    position: relative;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    border-radius: 30px;
    flex-direction: column;
    overflow: hidden;
}
.prices.nav-toggle{
    display: flex;
    transform: scale(1);
}
.location{
    position: relative;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    border-radius: 30px;
    flex-direction: column;
    overflow: hidden;
}
.location.nav-toggle{
    display: flex;
    transform: scale(1);
}

.whatsapp{
    position: relative;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    border-radius: 30px;
    flex-direction: column;
    overflow: hidden;
}
.whatsapp.nav-toggle{
    display: flex;
    transform: scale(1);
}
.users-save{
    position: relative;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    border-radius: 30px;
    flex-direction: column;
    overflow: hidden;
}
.users-save.nav-toggle{
    display: flex;
    transform: scale(1);
}


.div-imgs{
    position: absolute;
    top: 5%;
    background: #fff;
    width: 150px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 0 15px var(--shadow-color);
}
.div-imgs img{
width: 120px;
}
.title_names{
    text-align: center;
    margin-top: 170px;
}
.date-field{
    color: var(--secondary-text-color);
}
.title_names h3{
    margin-bottom: 10px;
    font-size: 25px;
    color: var(--secondary-text-color);
}
.title_names .marque{
    margin-top: 10px;
}

.title_names .marque p {
    color: var(--secondary-text-color);
}
.title_names .marque marquee{
    padding: 5px 0;
    color: var(--secondary-text-color);
    font-weight: 900;
}
.login form{
    width: 100%;
}
.input-box{
    position: relative;
    width: 100%;
    height: 40px;
    text-align: right;
    margin-top: 25px;
}
input{
    text-align: center;
}
option{
    color: #000;
}

.input-box select{
    background: 0; height: 40px; border: 0;color: var(--secondary-text-color); border-bottom: 2px solid; width: 100%; appearance: none; text-align: right;
}


.togswith {
    margin: 5px;
    justify-content: center;
    position: relative;
    padding: 5px
}

.togswith h3 , .togswith div {
    color: var(--secondary-text-color);
}

.switch {
    position: relative;
    display: inline-block;
    margin: 0 5px
}

.switch i {
    font-size: 16px;
    display: block;
    margin-top: -5px
}

.switch>span {
    position: absolute;
    top: 14px;
    pointer-events: none;
    font-family: 'Helvetica', Arial, sans-serif;
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
    text-shadow: 0 1px 0 rgba(0, 0, 0, .06);
    width: 50%;
    text-align: center
}

input.check-toggle-round-flat:checked~.off {
    color: #1bb10d
}

input.check-toggle-round-flat:checked~.on {
    color: #fff
}

.switch>span.on {
    left: 0;
    padding-left: 2px;
    color: #000
}

.switch>span.off {
    right: 0;
    padding-right: 4px;
    color: #fff
}

.check-toggle {
    position: absolute;
    margin-left: -9999px;
    visibility: hidden
}

.check-toggle+label {
    display: block;
    position: relative;
    cursor: pointer;
    outline: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

input.check-toggle-round-flat+label {
    padding: 2px;
    width: 60px;
    height: 35px;
    background-color: var(--primary-line-color);
    -webkit-border-radius: 60px;
    -moz-border-radius: 60px;
    -ms-border-radius: 60px;
    -o-border-radius: 60px;
    border-radius: 60px
}

input.check-toggle-round-flat+label:before,
input.check-toggle-round-flat+label:after {
    display: block;
    position: absolute;
    content: ""
}

input.check-toggle-round-flat+label:before {
    top: 2px;
    left: 2px;
    bottom: 2px;
    right: 2px;
    background-color: 0;
    -webkit-border-radius: 60px;
    -moz-border-radius: 60px;
    -ms-border-radius: 60px;
    -o-border-radius: 60px;
    border-radius: 60px
}

input.check-toggle-round-flat+label:after {
    top: 4px;
    left: 4px;
    bottom: 4px;
    width: 26px;
    background-color: #fff;
    -webkit-border-radius: 52px;
    -moz-border-radius: 52px;
    -ms-border-radius: 52px;
    -o-border-radius: 52px;
    border-radius: 52px;
    -webkit-transition: margin .2s;
    -moz-transition: margin .2s;
    -o-transition: margin .2s;
    transition: margin .2s
}

input.check-toggle-round-flat:checked+label:after {
    margin-left: 25px
}




#se {
    display: block;
    margin: 10px auto;
    width: 150px;
    border: 0;
    color: var(--primary-line-color);
    outline: 0;
    appearance: none;
    text-align: center;
    padding-bottom: 5px;
    background: url(../img/se.svg) no-repeat;
    background-size: 100% 100%;
    height: 25px
}

.input-box input{
    width: 100%;
    height: 100%;
    background: transparent;
    border: none;
    outline: none;
    font-size: 16px;
    color: var(--secondary-text-color);
    font-weight: 600;
    border-bottom: 2px solid var(--secondary-text-color);
    padding-right: 30px;
    transition: .7s ease;
}
.input-box input:focus,
.input-box input:valid {
    color: var(--primary-line-color);
    border-bottom-color: var(--primary-line-color);
}
.input-box label{
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    font-size: 16px;
    color: var(--secondary-text-color);
    pointer-events: none;
    transition: .7s ease;
}
.input-box .icon-eye-off{
cursor: pointer;
}
.input-box i{
    position: absolute; 
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    font-size: 16px;
    transition: .7s ease;
    color: var(--secondary-text-color)
}
.input-box input:focus ~ label,
.input-box input:valid ~ label{
    top: -5px;
    color: #ff2770;
}
.input-box input:focus ~ i,
.input-box input:valid ~ i{
    color: #ff2770;
}
.btn{
    position: relative;
    width: 100%;
    height: 45px;
    background: transparent;
    border-radius: 30px ;
    cursor: pointer;
    font-size: 16px;
    color: #fff;
    font-weight: 600;
    border: 2px solid #ff2770;
    overflow: hidden;
    z-index: 1;
}
.btn::before{
    content: "";
    position: absolute;
    width: 100%;
    height: 300%;
    background: linear-gradient(#25252b,#ff2770,#25252b,#ff2770);
    top: -100%;
    left: 0;
    z-index: -1;
    transition: .7s ease;
}
.btn:hover:before{
    top: 0;
}
.btn.trial{
display: flex;
align-items: center;
justify-content: center;
}
    .error-container {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.50);
        z-index: 999;
        display: -webkit-box; 
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex; 
        display: flex;
        align-items: center;
        justify-content: center;
        -webkit-justify-content: center;
        -moz-justify-content: center;
        -webkit-box-align: center;
        -moz-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        padding: 10px;
        animation: overlayed 0.3s linear;
    }
    .error-container .error{
        flex: 0 1 360px;
        max-width: 320px;
        background-color: var(--main-background);
        border-radius: 5px;
        padding: 10px;
        text-align: center;
        animation: modalAnimation 0.3s ease;
        box-shadow: 0 0 5px var(--shadow-color);
    }
    .error-container .error p{
        color: var(--secondary-text-color);
    }
    @keyframes overlayed {
        0%{opacity:0}
        100%{opacity:1}
    }
    @-webkit-keyframes overlayed {
        0%{opacity:0}
        100%{opacity:1}
    }
    @keyframes modalAnimation {
        0%{transform: translateY(-80px);}
        100%{transform: translateY(0);}
    }
    
    @-webkit-keyframes modalAnimation {
        0%{transform: translateY(-80px);}
        100%{transform: translateY(0);}
    }
    #dollar{
        position: fixed;
        top:1rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    #location{
        position: fixed;
        top:4.5rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    #whatsapp{
        position: fixed;
        top:8rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    #theme-toggler{
        position: fixed;
        top:11.5rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    
    #theme-toggler.icon-moon{
        transform:rotate(-180deg);
    }
    #users-save{
        position: fixed;
        top:15rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 2.6rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    #btn-logout{
        position: fixed;
        top:15rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.3rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
    #close-logout{
        position: fixed;
        top:18.5rem; right:1rem;
        z-index: 998;
        height: 3rem;
        width: 3rem;
        line-height: 3rem;
        text-align: center;
        font-size: 1.2rem;
        background:var(--primary-line-color);
        color:#fff;
        cursor: pointer;
        border-radius: 5rem;
    }
.prices .card{
    position: relative;
    background: var(--main-background);
    width: 100%;
    height: 120px;
    border-radius: 10px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 0 5px var(--shadow-color);
}
.prices .card .id{
    position: absolute;
    right: 10px;
    top: 2px;
    color: var(--secondary-text-color);
    font-size: 25px;
}
.prices .card .linescal{
    position: absolute;
    right: -40px;
    top: -40px;
    border-radius: 30px 0 30px 70px;
    background: none;
    border: 15px solid var(--primary-line-color);
    width: 100px;
    height: 100px;
    z-index: 2;
}
.prices .card .linescal1{
    position: absolute;
    right: -41px;
    top: -35px;
    border-radius: 30px 0 30px 70px;
    background: none;
    border: 15px solid var(--primary-line2-color);
    width: 100px;
    height: 100px;
}
.prices .card .linescal2{
    position: absolute;
    right: -51px;
    top: -46px;
    border-radius: 50px 0 50px 100px;
    background: none;
    border: 15px solid var(--primary-line-color);
    width: 140px;
    height: 140px;
    z-index: 2;
}
.prices .card .linescal3{
    position: absolute;
    right: -50px;
    top: -40px;
    border-radius: 50px 0 50px 100px;
    background: none;
    border: 15px solid var(--primary-line2-color);
    width: 140px;
    height: 140px;
}
.prices .card .text {
    position: absolute;
    right: 50%;
    top: 10px;
    color: var(--secondary-text-color);
}
.prices .card .text1 {
    position: absolute;
    right: 50%;
    top: 40px;
    color: var(--secondary-text-color);
}
.prices .card .text2 {
    position: absolute;
    right: 50%;
    top: 65px;
    color: var(--secondary-text-color);
}
.prices .card .text3 {
    position: absolute;
    right: 50%;
    top: 90px;
    color: var(--secondary-text-color);
}
.num1{
color: var(--primary-line-color);
}
.location .card{
    position: relative;
    background: var(--main-background);
    width: 100%;
    height: 60px;
    border-radius: 5px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 0 5px var(--shadow-color);
}
.location .card .linescal{
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 15px;
    background: var(--primary-line-color);
}
.location .card .linescal1{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
    border-radius: 30px 30px 0 0;
    width: 150px;
    height: 30px;
    background: var(--primary-line-color);
}
.location .card  .id{
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translate(-50%);
    color: #fff;
    font-size: 20px;
    z-index: 5;
}
.location .card .text{
    position: absolute;
    top: 4px;
    left: 50%;
    transform: translate(-50%);
    width: 100%;
    font-size: 18px;
    text-align: center;
    color: var(--secondary-text-color);
}
.whatsapp .card{
    position: relative;
    background: var(--main-background);
    width: 100%;
    height: 150px;
    border-radius: 10px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 0 5px var(--shadow-color);
}
.whatsapp .card .linescal{
    position: absolute;
    top: -32px;
    right: -70px;
    width: 100px;
    height: 100px;
    transform:rotate(-120deg);
    background: var(--primary-line2-color);
}
.whatsapp .card .linescal1{
    position: absolute;
    bottom: -17px;
    right: -86px;
    width: 140px;
    height: 100px;
    transform: rotate(-66deg);
    background: var(--primary-line-color);
}
.whatsapp .card .linescal2{
    position: absolute;
    top: 0;
    left: 0;
    clip-path: polygon(0 0, 100% 0%, 92% 100%, 0 100%);
    width: 140px;
    height: 10px;
    background: var(--primary-line-color);
}
.whatsapp .card .linescal3{
    position: absolute;
    bottom: -54px;
    left: -70px;
    width: 100px;
    height: 100px;
    transform:rotate(-120deg);
    background: var(--primary-line2-color);
}
.whatsapp .card .text{
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translate(-50%);
    color: var(--secondary-text-color);
}
.whatsapp .card .text1{
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translate(-50%);
    color: var(--secondary-text-color);
}
.whatsapp .card .text2{
    position: absolute;
    top: 90px;
    left: 50%;
    transform: translate(-50%);
    color: var(--secondary-text-color);
}
.users-save .card{
    position: relative;
    background: var(--main-background);
    width: 100%;
    height: 120px;
    border-radius: 10px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 0 5px var(--shadow-color);
}
.users-save .card .linescal{
    position: absolute;
    top: -34px;
    right: 5px;
    width: 42px;
    height: 140px;
    transform: rotate(139deg);
    background: var(--primary-line2-color);
}
.users-save .card .linescal1{
    position: absolute;
    bottom: -43px;
    right: -38px;
    width: 70px;
    height: 138px;
    transform: rotate(208deg);
    background: var(--primary-line-color);
}
.users-save .card .linescal2{
    position: absolute;
    bottom: -20px;
    right: 29px;
    width: 10px;
    height: 146px;
    transform: rotate(208deg);
    background: var(--primary-line-color);
}
.users-save .card .btn{
    position: absolute;
    bottom: 5px;
    left: 5px;
    width: 100px;
    height: 30px;
    border: none;
    border-radius: 5px;
}
.users-save .card .text{
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translate(-50%);
    color: var(--secondary-text-color);
}
.users-save .card .text1{
    position: absolute;
    top: 30px;
    left: 5px;
    width: 75%;
    text-align: right;
    direction: rtl;
    color: var(--secondary-text-color);
}
.users-save .card .text1 span{
    background: var(--primary-line2-color);
    padding: 1px 5px ;
    border-radius: 3px;
    margin-right: 2px;
    display: inline-block;
    width: 135px;
    color: var(--secondary-text2-color);
}
.users-save .card .text2{
    position: absolute;
    top: 60px;
    left: 0;
    width: 80%;
    text-align: right;
    direction: rtl;
    color: var(--secondary-text-color);
}
.users-save .card .text2 span{
    background: var(--primary-line2-color);
    padding: 1px 5px ;
    border-radius: 3px;
    margin-right: 2px;
    display: inline-block;
    width: 135px;
    color: var(--secondary-text2-color);
}
/* status */
.battery {
    width: 100%;
    height: 60px;
    border: 3px solid #333;
    border-radius: 5px;
    position: relative;
    display: flex;
    margin-bottom: 5px;
    align-items: center;
    justify-content: flex-start;
    padding: 3px;
    box-shadow: 0 0 5px var(--shadow-color);
}
.battery::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -15px;
    width: 10px;
    height: 20px;
    background: #333;
    transform: translateY(-50%);
    border-radius: 3px;
    box-shadow: 0 0 5px var(--shadow-color);
    /* z-index: -1; */
}
.battery-level {
    height: 100%;
    background: linear-gradient(to right, #bfd2ff, #99ccff, #023fa7);
    transition: width 0.5s;
    border-radius: 2px;
}
.battery-text {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-text-color);
}
.user-info{
    position: relative;
    background: var(--main-background);
    width: 100%;
    height: 100px;
    border-radius: 5px;
    margin-bottom: 5px;
    overflow: hidden;
    box-shadow: 0 0 5px var(--shadow-color);
}
.user-info .linescal{
    position: absolute;
    top: 0;
    right: 0;
    width: 90%;
    height: 15px;
    background: var(--primary-line-color);
}
.user-info .linescal1{
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    border-radius: 0 0 0 10px;
    height: 30px;
    background: var(--primary-line-color);
}
.user-info .linescal2{
    position: absolute;
    top: -12px;
    left: -2px;
    width: 50px;
    border-radius: 50%;
    height: 50px;
    background: var(--primary-line-color);
}
.user-info .text{
    position: absolute;
    top: 5px;
    right: 5px;
    border-radius: 50%;
    background: var(--primary-line-color);
}
.icon_status{
    color: #fff;
}
.user-info .icon_status{
    position: absolute;
    top: 5px;
    left: 9px;
    font-size: 20px;
    border-radius: 50%;
}
.user-info .line{
    position: absolute;
    top: 65%;
    left: 50%;
    transform: translate(-50%);
    width: 100%;
    height: 2px;
    background: var(--primary-line2-color);
}
.user-info .users{
    position: absolute;
    top: 43px;
    right: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content:space-between;
    padding: 0 10px;
    direction: rtl;
    color: var(--primary-line2-color);
}
.user-info .ip-user{
    position: absolute;
    top: 73px;
    right: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content:space-between;
    padding: 0 10px;
    direction: rtl;
    color: var(--primary-line2-color);
}
.uptime{
    height: 114px;
}
.uptime .line{
    top: 65px;
}
.temar-uptime{
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 12px;
}
.temar-uptime .uptime-level{
    height: 100%;
    background: linear-gradient(to right, #bfd2ff, #99ccff, #023fa7);
    transition: width 0.5s;
    border-radius: 5px;
}
    .wrap {
        width: 100%;
        border-radius: 10px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        text-align: center;
    }
    h3 {
        color: #007bff;
        margin: 5px;
    }
    table {
        width: 100%;
        border-collapse: collapse;
        color: var(--secondary-text-color);
    }
    td {
        padding: 2px;
        border: 1px solid var(--primary-line2-color);
    }