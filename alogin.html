<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
	<link rel="icon" type="image/ico" href="img/wifi.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="expires" content="-1">
    <title>تم تسجيل دخولك بنجاح</title>
    <link rel="stylesheet" href="css/fontello.css" />
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="fonts/Almarai.css">
</head>
<style>
.timer-container {
    position: relative;
    width: 100%;
    height: 100px;
    margin-top: 20px;
}
.circle-timer {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}
.circle-path {
    fill: none;
    stroke: #ccc;
    stroke-width: 4;
    stroke-dasharray: 188; /* محيط الدائرة */
    stroke-dashoffset: 0;
    transition: stroke-dashoffset 1s linear;
}
.circle-path.active {
    stroke: #ff2770;
}
.time-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: bold;
    color: #0bc0c0;
}
</style>
<body>
    <div class="box">
        <div class="outscreen">
        <div class="login" id="home">
            <div class="div-imgs"><img src="img/logo.png" alt="login"></div>
            <div class="title_names">
            <h3 >تم تسجيل الدخول بنجاح</h3>
            <div class="date-field" data-show-date-field><span hot-date></span> ,الموافق <span hot-hijri-date></span></div>
            <div class="timer-container">
                <svg class="circle-timer" viewBox="0 0 64 64">
                    <circle class="circle-path" cx="32" cy="32" r="30" />
                </svg>
                <div class="time-text">5</div>
            </div>
            <div class="marque" style="margin-top: 50px;">
                <p>سيتم تحويلك الى صفحة معلومات الكرت تلقائيا,اذا لم يتم تحويلك <span >اضغط على الزر</span></p>
            </div>
            </div>
                <div class="input-box">
                    <a href="status.html"><button type="submit" class="btn"> دخول</button></a>
                </div>
        </div></div>
        <div class="border-right"></div><div class="border-left"></div><div class="border-left-top"></div><div class="border-right-bottom"></div><div class="border-right-right"></div><div class="border-left-left"></div><div class="border-top-bottom"></div>
    </div>
    <script>
let timeLeft = 5;
const circle = document.querySelector('.circle-path');
const timeText = document.querySelector('.time-text');
function updateTimer() {
    const dashOffset = 188 - (timeLeft / 5) * 188; 
    circle.style.strokeDashoffset = dashOffset;
    timeText.textContent = timeLeft;

    if (timeLeft > 0) {
        timeLeft--;
        setTimeout(updateTimer, 1000);
    } else {
        window.location.href = "status.html";
    }
}
circle.classList.add('active');
updateTimer();
    </script>
<script src="js/jquery-3.7.1.min.js"></script>
<script src="js/script.js"></script>
<script src="js//hotOptions.min.js"></script>
</body>
</html> 