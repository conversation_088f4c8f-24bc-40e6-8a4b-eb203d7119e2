﻿var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};$jscomp.arrayIterator=function(a){return{next:$jscomp.arrayIteratorImpl(a)}};$jscomp.makeIterator=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):$jscomp.arrayIterator(a)};var k="الاحد الاثنين الثلاثاء الاربعاء الخميس الجمعة السبت".split(" ");
function fitText(a,b){a.style.fontSize=Math.ceil(10*a.clientWidth*(b||1)/a.innerText.length)+"px";return a}
function l(a){var b,g=null!=(b=a.getAttribute("hot-img-path"))?b:"1.jpg";a.style.textAlign="center";var c=new Image;c.src="adv_img/"+g;c.classList.add("adv-img");c.complete?a.innerHTML=c.outerHTML:(c.onload=function(){a.innerHTML=c.outerHTML},c.onerror=function(){document.querySelector(".logo").style.paddingTop="50px";var d=document.querySelector(".section");d.hasAttribute("enable-hot-marquee")&&(d.style.display="block")})}
function p(a){a.innerHTML=q();setInterval(function(){a.innerHTML=q()},1E3)}function q(){var a=new Date,b=("0"+a.getSeconds()).slice(-2),g=("0"+a.getMinutes()).slice(-2);a=("0"+a.getHours()).slice(-2);var c="صباحاً";12<a&&(a=("0"+(a-12)).slice(-2),c="مساءً");return"  الساعة الان "+a+":"+g+":"+b+" "+c}function startFitText(){var a=document.querySelectorAll("[data-fit-text]"),b;a=$jscomp.makeIterator(a);for(b=a.next();!b.done;b=a.next())b=b.value,fitText(b,.215)}
document.addEventListener("DOMContentLoaded",function(){var a=document.querySelector("[hot-date]");if(null!==a){var b=new Date;a.innerHTML=k[b.getDay()]+" "+b.getDate()+" "+"يناير فبراير مارس ابريل ماي يونيو يوليو اغسطس سبتمبر اكتوبر نوفمبر ديسمبر".split(" ")[b.getMonth()]+" "+b.getFullYear()}a=document.querySelector("[hot-time]");null!==a&&p(a);a=document.querySelector("div[hot-adv-img]");null!==a&&l(a);a=document.querySelector("[hot-hijri-date]");if(null!==a){var g;b=null!=(g=a.getAttribute("hot-adjust-days"))?
g:void 0;g=null!==a.getAttribute("hot-with-day");var c=new Date;b&&c.setDate(c.getDate()+ +b);b=c.getDate();var d=c.getMonth(),e=d+1;c=c.getFullYear();3>e&&(--c,e+=12);d=Math.floor(c/100);d=2-d+Math.floor(d/4);1583>c&&(d=0);1582===c&&(10<e&&(d=-10),10===e&&(d=0,4<b&&(d=-10)));e=Math.floor(365.25*(c+4716))+Math.floor(30.6001*(e+1))+b+d-1524;d=0;2299160<e&&(d=Math.floor((e-1867216.25)/36524.25),d=1+d-Math.floor(d/4));b=e+d+1524;c=Math.floor((b-122.1)/365.25);d=Math.floor(365.25*c);var f=Math.floor((b-
d)/30.6001);b=b-d-Math.floor(30.6001*f);d=f-1;13<f&&(c+=1,d=f-13);var h=10631/30;f=e-1948084;var m=Math.floor(f/10631);f-=10631*m;var n=Math.floor((f-.1335)/h);f-=Math.floor(n*h+.1335);h=Math.floor((f+28.5001)/29.5);13===h&&(h=12);b=[b,d-1,c-4716,e-1,((e+1)%7+7)%7+1-1,f-Math.floor(29.5001*h-29),h-1,30*m+n];e=b[5]+" "+"محرم;صفر;ربيع الأول;ربيع الثانى;جمادى الأولى;جمادى الثانية;رجب;شعبان;رمضان;شوال;ذو القعدة;ذو الحجة".split(";")[b[6]]+" "+b[7];g&&(e=k[b[4]]+", "+e);a.innerText=e}},!1);
