var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.createTemplateTagFirstArg=function(a){return a.raw=a};$jscomp.createTemplateTagFirstArgWithRaw=function(a,b){a.raw=b;return a};var BLOCKED_TIME="hot_blocker_blocked_time",BLOCKED_FORE="hot_blocker_block_minutes",FAIL_COUNTER="hot_blocker_counter";function incrementCounter(a){var b;a=null!=(b=a.getAttribute("block-time"))?b:5;var c;b=null!=(c=getCookieOrNull(FAIL_COUNTER))?c:0;b=parseInt(b)+1;setCookie(FAIL_COUNTER,b,a);return b}
function checkFailsCount(a,b){var c,e=null!=(c=a.getAttribute("try-count"))?c:5;if(!(b<=e)){var d;c=null!=(d=a.getAttribute("block-time"))?d:5;d=timeStamp();setCookie(BLOCKED_TIME,d,c);setCookie(BLOCKED_FORE,c,c);redirectToBlockPage(d)}}function resetCounter(){setCookie(BLOCKED_TIME,"",0);setCookie(FAIL_COUNTER,"",0)}function isLoggedIn(){return null!==document.querySelector("script[clear-hot-blocker]")}function redirectToBlockPage(a){window.location.href="block.html"}
function checkIsBlocked(){var a=getCookieOrNull(BLOCKED_TIME);if(null==a)return!1;redirectToBlockPage(a);return!0}function setCookie(a,b,c){var e=new Date;e.setTime(e.getTime()+6E4*c);c="expires="+e.toUTCString();document.cookie=a+"="+b+";"+c+";path=/"}function getCookieOrNull(a){var b=null;document.cookie.split(";").forEach(function(c){c=c.trim().split("=");if(c[0]===a)return b=c[1],!0});return b}function timeStamp(){return Math.floor((new Date).getTime())}
function initCountDown(a){var b=getCookieOrNull(BLOCKED_TIME);if(null===b)window.location.href="login.html";else var c=6E4*getCookieOrNull(BLOCKED_FORE)+parseInt(b),circleEl = document.querySelector(".circle-path"),totalTime = c - timeStamp(),e=setInterval(function(){var d=timeStamp();d=c-d;var minutes = Math.floor(d % 36E5 / 6E4);var seconds = Math.floor(d % 6E4 / 1E3);a.innerHTML = minutes + ":" + (seconds < 10 ? "0" + seconds : seconds);if (circleEl) {updateCircle(totalTime, d, circleEl);}
0>d&&(clearInterval(e),window.location.href="login.html")},1E3)}document.addEventListener("DOMContentLoaded",function(){var a=document.querySelector("span[count-down-span]");if(null!==a)initCountDown(a);else if(isLoggedIn())resetCounter();else if(!checkIsBlocked()&&(a=document.querySelector("script[login-error]"),null!==a&&""!==a.getAttribute("login-error"))){var b=incrementCounter(a);checkFailsCount(a,b)}},!1);
const CIRCLE_DASHARRAY = 188; function updateCircle(totalTime, timeLeft, circleEl) {var dashOffset = CIRCLE_DASHARRAY - (timeLeft / totalTime) * CIRCLE_DASHARRAY;circleEl.style.strokeDashoffset = dashOffset;}