*,*:before,*:after{
    box-sizing:border-box;
    margin:0;
    padding:0;
    /*transition*/
    -webkit-transition:.25s ease-in-out;
       -moz-transition:.25s ease-in-out;
         -o-transition:.25s ease-in-out;
            transition:.25s ease-in-out;
    outline:none;
    }
#toggles{
    width:50px;
    margin:auto;
    text-align:center;
}
.ios-toggle,.ios-toggle:active{
    height:0;
    width:0;
    opacity:0;
    border:none;
    outline:none;
}
.checkbox-label{
    display:block;
    position:relative;
    padding:10px;
    margin-bottom:20px;
    font-size:14px;
    line-height:8px;
    width:100%;
    height:25px;
    -webkit-border-radius:18px;
    -moz-border-radius:18px;
    border-radius:18px;
    background: var(--primary-line2-color);
    cursor:pointer;
}
.checkbox-label:before{
    content:'';
    display:block;
    position:absolute;
    z-index:1;
    line-height:34px;
    text-indent:40px;
    height:25px;
    width:25px;
    /*border-radius*/
    -webkit-border-radius:100%;
    -moz-border-radius:100%;
    border-radius:100%;
    top:0px;
    left:0px;
    right:auto;
    background: var(--primary-line2-color);
    -webkit-box-shadow:0 3px 3px rgba(0,0,0,.2),0 0 0 2px #dddddd;
    -moz-box-shadow:0 3px 3px rgba(0,0,0,.2),0 0 0 2px #dddddd;
    box-shadow:0 3px 3px rgba(0,0,0,.2),0 0 0 2px #dddddd;
    }
.checkbox-label:after{
        content:attr(data-off);
        display:block;
        position:absolute;
        z-index:0;
        top:0;
        left:-300px;
        padding:10px;
        height:100%;
        width:300px;
        text-align:right;
        color:var(--secondary-text-color);
        white-space:nowrap;
    }
.ios-toggle:checked + .checkbox-label{
        -webkit-box-shadow:inset 0 0 0 20px #ff2770,0 0 0 2px #ff2770;
        -moz-box-shadow:inset 0 0 0 20px #ff2770,0 0 0 2px #ff2770;
        box-shadow:inset 0 0 0 20px #ff2770,0 0 0 2px #ff2770;
 }
 .ios-toggle:checked + .checkbox-label:before{
    left:calc(100% - 25px);
    -webkit-box-shadow:0 0 0 2px transparent,0 3px 3px rgba(0,0,0,.3);
    -moz-box-shadow:0 0 0 2px transparent,0 3px 3px rgba(0,0,0,.3);
    box-shadow:0 0 0 2px transparent,0 3px 3px rgba(0,0,0,.3);
    }
.ios-toggle:checked + .checkbox-label:after{
        content:attr(data-on);
        left:50px;
        width:36px;
    }
#remember + .checkbox-label{
        -webkit-box-shadow:inset 0 0 0 0px #ff2770,0 0 0 2px #dddddd;
           -moz-box-shadow:inset 0 0 0 0px #ff2770,0 0 0 2px #dddddd;
                box-shadow:inset 0 0 0 0px #ff2770,0 0 0 2px #dddddd;
    }
#remember:checked + .checkbox-label{
        /*box-shadow*/
        -webkit-box-shadow:inset 0 0 0 18px #ff2770,0 0 0 2px #ff2770;
           -moz-box-shadow:inset 0 0 0 18px #ff2770,0 0 0 2px #ff2770;
                box-shadow:inset 0 0 0 18px #ff2770,0 0 0 2px #ff2770;
        }
#remember:checked + .checkbox-label:after{
            color:#ff2770;
            }