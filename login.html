<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0' name='viewport' />
    <link rel="icon" type="image/ico" href="img/wifi.png">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>تسجيل الدخول</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/chik.css">
    <link rel="stylesheet" href="css/fontello.css">
    <link rel="stylesheet" href="font/Almarai.css">
    <script src="js/hotCookie.min.js"></script>
    <script src="js/hotInImprover.min.js"></script>
    <script src="js/hotBlocker.min.js" login-error="$(error)" try-count="2" block-time="3"></script> 
</head>
<body>
<!-- $(if chap-id) -->
<form name="sendin" action="$(link-login-only)" method="post">
    <input type="hidden" name="username" />
    <input type="hidden" name="password" />
    <input type="hidden" name="dst" value="$(link-orig)" />
    <input type="hidden" name="popup" value="true" />
    <input type="hidden" name="domain" />
</form>
<script type="text/javascript" src="/md5.js"></script>
<script type="text/javascript">
    //<!--
    function doLogin() {
        userLogin();
        document.sendin.username.value = document.login.username.value;
        document.sendin.password.value = hexMD5('$(chap-id)' + document.login.password.value + '$(chap-challenge)');
        document.sendin.domain.value = document.login.domain.value + document.login.update.value;
        document.sendin.submit();
        return false;
	}
//-->
</script>
<!-- $(endif) -->
    <div id="dollar" class=" icon-dollar dollar" title="الاسعار"></div>
    <div id="location" class="icon-location" title="نقاط البيع"></div>
    <div id="whatsapp" class="icon-whatsapp" title="تواصل معنا"></div>
    <div id="theme-toggler" class="icon-sun" title="الوضع الداكن"></div>
    
    <div class="box">
    <div class="outscreen">
        <div class="prices" id="alert-prices">
        <div class="scrols">
            <div class="card">
                <div class="id">1</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 200 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 10 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 600 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 6 </span><span>ايام</span></div>
            </div>
            <div class="card">
                <div class="id">2</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 300 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 15 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 800 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 8 </span><span>ايام</span></div>
            </div>
            <div class="card">
                <div class="id">3</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 500 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 20 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 1500 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 12 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">4</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 1000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 30 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 3000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 24 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">5</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 3000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 45 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 9000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 30 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">6</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 5000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 30 </span><span>يوم</span></div>
                <div class="text2">الرصيد <span class="num1"> 15000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 30 </span><span>يوم</span></div>
            </div>
        </div>
        </div>
        <div class="location">
            <div class="scrols">
            <div class="card">
                <div class="id">1</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">2</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">3</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">4</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">5</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">6</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">7</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">8</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">9</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">10</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
        </div>
        </div>
        <div class="whatsapp">
            <div class="scrols">
            <div class="card">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">تواصل معنا</div>
                <div class="text1">الادراة<span class="num1"> : </span><span>777 777 777</span></div>
                <div class="text2">الصيانة<span class="num1"> : </span><span>777 777 777</span></div>
            </div>

        </div>
        </div>
        <div class="login" id="home">
            <div class="div-imgs"><img src="img/logo.png" alt="login"></div>
            <div class="title_names">
            <h3>جوجل نت </h3>
            <div class="date-field" data-show-date-field><span hot-date></span> ,الموافق <span hot-hijri-date></span></div>
            <div class="marque">
                <marquee direction="right" onMouseOver="this.stop();" onMouseOut="this.start();"> اهلا وسهلا بكم في شبكة اسم الشبكة اللاسلكية </marquee>
            </div>
            </div>
            <form name="login" action="$(link-login-only)" method="post" 
            $(if chap-id) onSubmit="return doLogin()" $(endif)>
                <input type="hidden" name="dst" value="$(link-orig)" />
                <input type="hidden" name="popup" value="true" />
                <select name="" id="se">
                </select>
                <div class="input-box">
                    <input class="in_user" type="text" id="username" name="username" username-field required improve-input rm-white-spaces to-lower  to-arabic-numbers maxlength="20" autocomplete="off" />
                    <label for="username">رقم الكرت</label>
                    <i class="icon-user"></i>
                </div>

                <div class="input-box">
                    <select class="in_user" id="speed" name="domain"
                    speed-field>
                    <option value="" selected disabled hidden>أختيار سرعة الإنترنت</option>
                    <option value="512K/512K">سرعة أقتصادية </option>
                    <option value="1M/4M">سرعة متوسطة</option>
                    <option value="0/0">سرعة عالية</option>
                </select>
                    <i class="icon-download"></i>
                </div>

                <div class="togswith">
    
                    <div class="btnu"
                        style="display: flex; height: 40px; justify-content: center; gap: 30px; align-items: center;">
                        
                        <div class="switch">
                            <input id="chupdate" name="updates" value="" class="check-toggle check-toggle-round-flat"
                                type="checkbox">
                                
                                <label for="chupdate"></label><span class="on">لا </span><span
                                class="off">نعم
                            </span></div>
<div>
                            <h3 >ايقاف التحديثات</h3>
                        </div>
                    </div>
                    <div style="text-align: center; margin: 2px auto; height: 20px; font-size: 12px; margin-bottom: -15px;" id="updatespan"></div>
                </div>
               


                <!-- <div class="input-box"> -->
                    <input class="in_pass"  type="hidden" id="password" name="password" />
                    <!-- <label for="password">كلمه المرور</label> 
                    <i class="icon-eye-off" id="showpassword"></i> 
                </div> -->
                <!-- <div id="toggles">
                    <input type="checkbox" id="remember" class="ios-toggle" value="ON" checked/>
                    <label for="remember" class="checkbox-label" data-off="عدم حفظ الكرت" data-on="حفظ الكرت"></label>
                </div> -->
                <div class="input-box">
                    <button type="submit" class="btn" onclick="return rem();">تسجيل الدخول</button>
                </div>
                <!-- $(if trial == 'yes') -->
                <!-- <div class="input-box">
                    <style>
                        .box{ height: 690px;}
                    </style>
                    <br><a href="$(link-login-only)?dst=$(link-orig-esc)&amp;username=T-$(mac-esc)" class="btn trial">تجربة الانترنت</a>
                </div> -->
                <!-- $(endif)  -->
            </form>
            <br>
            <h3>خدمة العملاء: 715563779 </h3>
        </div>
    </div>
        <div class="border-right"></div><div class="border-left"></div><div class="border-left-top"></div><div class="border-right-bottom"></div><div class="border-right-right"></div><div class="border-left-left"></div><div class="border-top-bottom"></div>
    </div>
    <!-- $(if error) -->
    <div class="error-container" onclick="this.style.display = ('none')">
        <div class="error">
            <div class="fade-in">
               <p style="margin-top: 5px;" id="error_msg">$(error)</p>
            </div>
        </div>
    </div>
    <!-- $(endif)   -->
    <script type="text/javascript">
function toArabicError(e) {
    var i, t = {
            "user&not found": "لقد ادخلت الكرت بطريقة غير صحيحة، الرجاء المحاولة مرة اخرى",
            "simultaneous session limit reached|no more sessions are allowed": "المعذرة ، هذا الكرت مستخدم حالياً في جهاز آخر",
            "invalid password": "تاكد من كتابة كلمة المرور بشكل صحيح",
            "uptime limit reached|No more online time|uptime limit": "عذراً لقد انتهى الوقت المتاح لك",
            "traffic limit|transfer limit reached": "لقد انتهى رصيد هذا الكرت",
            "invalid username or password|not found": "لقد ادخلت اسم المستخدم بطريقة غير صحيحة، الرجاء المحاولة مرة اخرى",
            "no valid profile found": "لقد انتهت صلاحية هذا الكرت",
            "invalid Calling-Station-Id": "هذا الكرت مقترن بجهاز آخر!",
            "server&is&not&responding": "يرجى الانتظار، يتم الآن اعادة تشغيل الشبكة، هذه العملية قد تستغرق بعض الوقت",
            "web&browser&did&not&send": "يرجى محاولة ادخال الكرت مرة اخرى"
        },
        o = "حصل خطأ: " + e;
    for (i in t) {
        var n = t[i];
        if (i.indexOf("&") > -1) {
            for (var r = i.split("&"), s = !0, a = 0; a < r.length; a++)
                if (-1 === e.indexOf(r[a])) {
                    s = !1;
                    break
                } if (s) {
                o = n;
                break
            }
        } else if (i.indexOf("|") > -1) {
            for (var u = i.split("|"), d = 0; d < u.length; d++)
                if (e.indexOf(u[d]) > -1) {
                    o = n;
                    break
                }
        } else if (e.indexOf(i) > -1) {
            o = n;
            break
        }
    }
    return o
}
            var elm = document.getElementById("error_msg");
                if(elm !== null) {
                elm.innerHTML = toArabicError(elm.textContent);
            }

        

function getStoredValuesFromCookie() {
    var e = document.cookie;
    if (e)
        for (var t = e.split(";"), r = 0; r < t.length; r++) {
            var n = t[r].trim();
            if (0 === n.indexOf("storedValues=")) {
                var o = n.substring("storedValues=".length);
                return JSON.parse(o)
            }
        }
    return []
}

var retrievedValues = getStoredValuesFromCookie();

function populateSelectWithOptions() {
    var e = document.getElementById("se");
    null != getStoredValuesFromCookie().reverse()[0] && (document.getElementById("uname").value =
            getStoredValuesFromCookie().reverse()[0]), e.innerHTML =
        '<option value="" selected disabled >دخول بكرت سابق</option>';
    for (var t = getStoredValuesFromCookie().reverse(), r = 0; r < t.length; r++) {
        var n = document.createElement("option");
        n.value = t[r], n.text = t[r], 0 == r && (n.text = "اخر كرت :" + t[r]), e.appendChild(n)
    }
    e.innerHTML += '<option value="relod">تحديث</option>'
}
populateSelectWithOptions(), document.getElementById("se").addEventListener("change", (function (e) {
    "relod" !== e.target.value ? (document.getElementById("uname").value = e.target.value, e.target
        .selectedIndex = 0) : window.location.href = "login.html"
}));

document.getElementById("chupdate").addEventListener("change", (function (e) {
    var t = document.getElementById("updatespan");
    e.target.checked ? (t.innerText =
        " تم اختيار أيقاف المتجر و التحديثات *لن يعمل متجر التطبيقات و يمكنك تغيير ذلك لاحقا*", e.target
        .value = "_Uoff") : (t.innerText = "تم اختيار تفعيل المتجر و التحديثات ", e.target
        .value = ""), showErrorPopup()
})),
        document.getElementById("uname").addEventListener("input", (function () {
            var e = this.value;
            this.hasAttribute("rm-white-spaces") && (e = e.split(" ").join("")), this
                .hasAttribute("to-lower") && (e = e.toLowerCase()), this.hasAttribute(
                    "to-upper") && (e = e.toUpperCase()), this.hasAttribute(
                "to-arabic-numbers") && (e = e.replace(RegExp(":", "g"), "0").replace(
                    new RegExp(String.fromCharCode(1632), "g"), "0").replace(new RegExp(
                    String.fromCharCode(1633), "g"), "1").replace(new RegExp(String
                    .fromCharCode(1634), "g"), "2").replace(new RegExp(String.fromCharCode(
                    1635), "g"), "3").replace(new RegExp(String.fromCharCode(1636), "g"),
                    "4").replace(new RegExp(String.fromCharCode(1637), "g"), "5").replace(
                    new RegExp(String.fromCharCode(1638), "g"), "6").replace(new RegExp(
                    String.fromCharCode(1639), "g"), "7").replace(new RegExp(String
                    .fromCharCode(1640), "g"), "8").replace(new RegExp(String.fromCharCode(
                    1641), "g"), "9")), this.hasAttribute("only-numbers") && (e = e.replace(
                    /\D/g, "")), this.hasAttribute("no-numbers") && (e = e.replace(/[0-9]/g,
                    "")), this.hasAttribute("only-alphanumeric") && (e = e.replace(
                    /[^0-9a-z]/gi, "")), this.value = e
        }))


            // document.login.username.focus();
            </script>
 <script src="js/jquery-3.7.1.min.js"></script>
<script src="js/script.js"></script>
<script src="js//hotOptions.min.js"></script>
        </body>
</html>