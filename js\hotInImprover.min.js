function b(){document.querySelectorAll("input[improve-input]").forEach(function(c){c.addEventListener("input",function(){var a=this.value;this.hasAttribute("rm-white-spaces")&&(a=a.split(" ").join(""));this.hasAttribute("to-lower")&&(a=a.toLowerCase());this.hasAttribute("to-upper")&&(a=a.toUpperCase());this.hasAttribute("to-arabic-numbers")&&(a=a.replace(RegExp(":","g"),"0").replace(new RegExp(String.fromCharCode(1632),"g"),"0").replace(new RegExp(String.fromCharCode(1633),"g"),"1").replace(new RegExp(String.fromCharCode(1634),
    "g"),"2").replace(new RegExp(String.fromCharCode(1635),"g"),"3").replace(new RegExp(String.fromCharCode(1636),"g"),"4").replace(new RegExp(String.fromCharCode(1637),"g"),"5").replace(new RegExp(String.fromCharCode(1638),"g"),"6").replace(new RegExp(String.fromCharCode(1639),"g"),"7").replace(new RegExp(String.fromCharCode(1640),"g"),"8").replace(new RegExp(String.fromCharCode(1641),"g"),"9"));this.hasAttribute("only-numbers")&&(a=a.replace(/\D/g,""));this.hasAttribute("no-numbers")&&(a=a.replace(/[0-9]/g,
    ""));this.hasAttribute("only-alphanumeric")&&(a=a.replace(/[^0-9a-z]/gi,""));this.value=a;console.log("value changed: "+this.value)})})}document.addEventListener("DOMContentLoaded",function(){b()},!1);
function hideHalfCard(a) {
    a = a.toLowerCase();
    if (0 <= a.indexOf("T-")) return "تجربة مجانية";
    if (0 <= a.indexOf(":")) return "اشتراك";
    var b = Math.ceil(a.length / 2);
    a = a.substring(0, b);
    return a += "*".repeat(b)
};




  