@font-face {
  font-family: 'fontello';
  src: url('../font/fontello.eot?78099305');
  src: url('../font/fontello.eot?78099305#iefix') format('embedded-opentype'),
       url('../font/fontello.woff2?78099305') format('woff2'),
       url('../font/fontello.woff?78099305') format('woff'),
       url('../font/fontello.ttf?78099305') format('truetype'),
       url('../font/fontello.svg?78099305#fontello') format('svg');
  font-weight: normal;
  font-style: normal;
}
/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */
/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */
/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'fontello';
    src: url('../font/fontello.svg?78099305#fontello') format('svg');
  }
}
*/
[class^="icon-"]:before, [class*=" icon-"]:before {
  font-family: "fontello";
  font-style: normal;
  font-weight: normal;
  speak: never;

  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  margin-right: .2em;
  text-align: center;
  /* opacity: .8; */

  /* For safety - reset parent styles, that can break glyph codes*/
  font-variant: normal;
  text-transform: none;

  /* fix buttons height, for twitter bootstrap */
  line-height: 1em;

  /* Animation center compensation - margins should be symmetric */
  /* remove if not needed */
  margin-left: .2em;

  /* you can be more comfortable with increased icons size */
  /* font-size: 120%; */

  /* Font smoothing. That was taken from TWBS */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-user:before { content: '\e800'; } /* '' */
.icon-download:before { content: '\e801'; } /* '' */
.icon-eye-off:before { content: '\e802'; } /* '' */
.icon-eye:before { content: '\e803'; } /* '' */
.icon-clock:before { content: '\e804'; } /* '' */
.icon-left-open:before { content: '\e805'; } /* '' */
.icon-credit-card:before { content: '\e806'; } /* '' */
.icon-phone:before { content: '\e807'; } /* '' */
.icon-power:before { content: '\e808'; } /* '' */
.icon-download-1:before { content: '\e809'; } /* '' */
.icon-login:before { content: '\e80a'; } /* '' */
.icon-info-circled:before { content: '\e80b'; } /* '' */
.icon-cancel-circle:before { content: '\e80e'; } /* '' */
.icon-ok-circle:before { content: '\e80f'; } /* '' */
.icon-location:before { content: '\f031'; } /* '' */
.icon-dollar:before { content: '\f155'; } /* '' */
.icon-sun:before { content: '\f185'; } /* '' */
.icon-moon:before { content: '\f186'; } /* '' */
.icon-copyright:before { content: '\f1f9'; } /* '' */
.icon-whatsapp:before { content: '\f232'; } /* '' */
