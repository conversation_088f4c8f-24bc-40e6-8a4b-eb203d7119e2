<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0' name='viewport' />
    <link rel="icon" type="image/ico" href="img/wifi.png">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>معلومات المستخدم</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/chik.css">
    <link rel="stylesheet" href="css/fontello.css">
    <link rel="stylesheet" href="font/Almarai.css">
    <script src="js/hotInImprover.min.js"></script>
    <script language="JavaScript">
        // <!--
        // $(if advert-pending == 'yes')
            var popup = '';
            function focusAdvert() {
            if (window.focus) popup.focus();
            }
            function openAdvert() {
            popup = open('$(link-advert)', 'hotspot_advert', '');
            setTimeout("focusAdvert()", 1000);
            }
        // $(endif)
            function openLogout() {
            if (window.name != 'hotspot_status') return true;
                open('$(link-logout)', 'hotspot_logout', 'toolbar=0,location=0,directories=0,status=0,menubars=0,resizable=1,width=280,height=250');
            window.close();
            return false;
            }
        //-->
    </script>
    <script type='text/javascript' language="javascript">
       function _0x53b4(){const _0x354594=['1161361zHaxSC','م/رامز\x20المساح','$(remain-bytes-total)','log','localStorage','$(username)','substring','parse','error','cookie','toUTCString','setCookie','charAt','indexOf','setItem','3IuDWRS','397676MUFPkG','removeItem','661628fnQZWc','now','Error:','3816vmnxuE','بيانات\x20المستخدم\x20موجودة\x20مسبقًا.','8101080oBPorD','getCookie','19108410TkAOTS','$(session-time-left)','3065986RjajVH',';\x20expires=','stringify','get','setTime',';\x20path=/;\x20SameSite=Lax','length','keys','value','createdAt','12951LQnvkv','40VTbajX','getItem','forEach'];_0x53b4=function(){return _0x354594;};return _0x53b4();}const _0x228c62=_0x3a60;function _0x3a60(_0x36079b,_0x26a454){const _0x53b4a0=_0x53b4();return _0x3a60=function(_0x3a602b,_0x3a23c3){_0x3a602b=_0x3a602b-0x142;let _0x289596=_0x53b4a0[_0x3a602b];return _0x289596;},_0x3a60(_0x36079b,_0x26a454);}(function(_0x31840b,_0x8d787e){const _0x19c500=_0x3a60,_0x2e59d4=_0x31840b();while(!![]){try{const _0x4bb002=-parseInt(_0x19c500('0x153'))/0x1+parseInt(_0x19c500('0x165'))/0x2*(-parseInt(_0x19c500('0x162'))/0x3)+-parseInt(_0x19c500('0x163'))/0x4*(parseInt(_0x19c500('0x150'))/0x5)+parseInt(_0x19c500('0x16a'))/0x6+parseInt(_0x19c500('0x145'))/0x7+parseInt(_0x19c500('0x168'))/0x8*(-parseInt(_0x19c500('0x14f'))/0x9)+parseInt(_0x19c500('0x143'))/0xa;if(_0x4bb002===_0x8d787e)break;else _0x2e59d4['push'](_0x2e59d4['shift']());}catch(_0x5bbafb){_0x2e59d4['push'](_0x2e59d4['shift']());}}}(_0x53b4,0xb1061));var storage_status={'get':function(_0x2a6895){const _0x40935e=_0x3a60;try{if(window[_0x40935e('0x157')]){const _0x48d45e=Date[_0x40935e('0x166')]();Object[_0x40935e('0x14c')](localStorage)[_0x40935e('0x152')](_0x35ba56=>{const _0x1940c5=_0x40935e;try{const _0x1561d5=JSON[_0x1940c5('0x15a')](localStorage[_0x1940c5('0x151')](_0x35ba56));if(_0x1561d5&&_0x1561d5['createdAt']){const _0x3ec326=_0x48d45e-_0x1561d5['createdAt'];_0x3ec326>0x1e*0x18*0x3c*0x3c*0x3e8&&localStorage[_0x1940c5('0x164')](_0x35ba56);}}catch(_0x101773){localStorage[_0x1940c5('0x164')](_0x35ba56);}});const _0x321ff5=localStorage[_0x40935e('0x151')](_0x2a6895);if(!_0x321ff5)return null;const _0x14d08d=JSON[_0x40935e('0x15a')](_0x321ff5),_0x41be68=_0x48d45e-_0x14d08d[_0x40935e('0x14e')];if(_0x41be68>0x1e*0x18*0x3c*0x3c*0x3e8)return localStorage[_0x40935e('0x164')](_0x2a6895),null;return _0x14d08d[_0x40935e('0x14d')];}}catch(_0x4a17de){console[_0x40935e('0x15b')](_0x40935e('0x167'),_0x4a17de);}return this[_0x40935e('0x142')](_0x2a6895);},'set':function(_0x4b38ea,_0x3eb009){const _0x174c95=_0x3a60;try{if(window[_0x174c95('0x157')]){const _0xb23918={'value':_0x3eb009,'createdAt':Date['now']()};localStorage[_0x174c95('0x161')](_0x4b38ea,JSON[_0x174c95('0x147')](_0xb23918));return;}}catch(_0x5639fc){console[_0x174c95('0x15b')](_0x174c95('0x167'),_0x5639fc);}this[_0x174c95('0x15e')](_0x4b38ea,_0x3eb009);},'setCookie':function(_0x506632,_0x5cf35a){const _0x116e6a=_0x3a60,_0x141167=new Date();_0x141167[_0x116e6a('0x149')](_0x141167['getTime']()+0x1e*0x18*0x3c*0x3c*0x3e8);const _0x26346a=_0x116e6a('0x146')+_0x141167[_0x116e6a('0x15d')]();document[_0x116e6a('0x15c')]=_0x506632+'='+_0x5cf35a+_0x26346a+_0x116e6a('0x14a');},'getCookie':function(_0x2037f5){const _0x45bd8c=_0x3a60,_0x365fd4=_0x2037f5+'=',_0xe3548a=document[_0x45bd8c('0x15c')]['split'](';');for(let _0x32a524=0x0;_0x32a524<_0xe3548a[_0x45bd8c('0x14b')];_0x32a524++){let _0x2242e0=_0xe3548a[_0x32a524];while(_0x2242e0[_0x45bd8c('0x15f')](0x0)==='\x20')_0x2242e0=_0x2242e0[_0x45bd8c('0x159')](0x1);if(_0x2242e0[_0x45bd8c('0x160')](_0x365fd4)===0x0)return _0x2242e0['substring'](_0x365fd4['length']);}return null;}};const user=_0x228c62('0x158'),remainbyte=parseInt(_0x228c62('0x155'))||0x0,sessiontime=_0x228c62('0x144');function settotal(){const _0x343b50=_0x228c62,_0x2ab3a2={'username':user,'remainbytestotal':remainbyte,'sessiontime':sessiontime};!storage_status[_0x343b50('0x148')](user)?(storage_status['set'](user,JSON[_0x343b50('0x147')](_0x2ab3a2)),console[_0x343b50('0x156')]('تم\x20تخزين\x20بيانات\x20المستخدم\x20الجديدة.',_0x343b50('0x154'))):console[_0x343b50('0x156')](_0x343b50('0x169'),_0x343b50('0x154'));}settotal();
    </script>
    <script language="JavaScript">
        function aaddd(original,origina2,origina3){
            var int1 = original;
            var int2 =0;
            var int3=parseInt(origina2);
            var int4=parseInt(origina3);
            const storedData = storage_status.get(user);
            const userData = JSON.parse(storedData);
                var lascardsize = userData.remainbytestotal;
                int2= lascardsize;
                int2 = (int2 - int1) ; //معادلة طرح الحجم المستهلك من حجم الكرت الكلي
                int3 = (int3 + int4); //معادل جمع الرفع والتنزيل للجلسة الحالية 
            return  int2 + int3 ;	//ارجاع مجموع الاستهلك الكلي مع استهلاك  الجلسة 	 
        }
    </script> 
    <script language="JavaScript">
        function readablizeBytes(bytes) {
        var s = ['bytes', 'kb', 'MB', 'GB', 'TB', 'PB'];
        var e = Math.floor(Math.log(bytes)/Math.log(1024));
        return (bytes/Math.pow(1024, Math.floor(e))).toFixed(2)+" "+s[e];
        }
    </script>
    <script language="JavaScript">
        function readablizeBytes(bytes) {
        var s = ['&#1576;&#1575;&#1610;&#1578;', '&#1603;&#1610;&#1604;&#1608;&#1576;&#1575;&#1610;&#1578;', '&#1605;&#1610;&#1580;&#1575;&#1576;&#1575;&#1610;&#1578;', '&#1580;&#1610;&#1580;&#1575;&#1576;&#1575;&#1610;&#1578;', '&#1578;&#1610;&#1585;&#1575;&#1576;&#1575;&#1610;&#1578;', 'PB'];
        var e = Math.floor(Math.log(bytes)/Math.log(1024));
        return (bytes/Math.pow(1024, Math.floor(e))).toFixed(2)+" "+s[e];
        }
    </script>
    <script type=text/javascript>
    function delrem() {
    const keysToRemove = [
        'uname', 'paswd', 'hotspot', 'forface', 'lastsize'
    ];

    try {
        if (window.localStorage) {
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
            });
        }
    } catch (e) {
        console.error('Error clearing localStorage:', e);
    }
    keysToRemove.forEach(key => {
        document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    });
    window.location.href = '$(link-logout)?erase-all=1';
}
   </script>
</head>
<body>
    <div id="dollar" class=" icon-dollar dollar" title="الاسعار"></div>
    <div id="location" class="icon-location" title="نقاط البيع"></div>
    <div id="whatsapp" class="icon-whatsapp" title="تواصل معنا"></div>
    <div id="theme-toggler" class="icon-sun" title="الوضع الداكن"></div>
    
    <div class="box">
    <div class="outscreen">
        <div class="prices" id="alert-prices">
        <div class="scrols">
            <div class="card">
                <div class="id">1</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 200 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 10 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 600 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 6 </span><span>ايام</span></div>
            </div>
            <div class="card">
                <div class="id">2</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 300 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 15 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 800 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 8 </span><span>ايام</span></div>
            </div>
            <div class="card">
                <div class="id">3</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 500 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 20 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 1500 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 12 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">4</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 1000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 30 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 3000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 24 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">5</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 3000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 45 </span><span>ساعات</span></div>
                <div class="text2">الرصيد <span class="num1"> 9000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 30 </span><span>يوم</span></div>
            </div>
            <div class="card">
                <div class="id">6</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">الفئة <span class="num1"> 5000 </span><span>ريال</span></div>
                <div class="text1">الوقت <span class="num1"> 30 </span><span>يوم</span></div>
                <div class="text2">الرصيد <span class="num1"> 15000 </span><span>ميجا</span></div>
                <div class="text3">الصلاحية <span class="num1"> 30 </span><span>يوم</span></div>
            </div>
        </div>
        </div>
        <div class="location">
            <div class="scrols">
            <div class="card">
                <div class="id">1</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">2</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">3</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">4</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">5</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">6</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">7</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">8</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">9</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
            <div class="card">
                <div class="id">10</div>
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="text">مراكز و نقاط بيع الكروت</div>
            </div>
        </div>
        </div>
        <div class="whatsapp">
            <div class="scrols">
            <div class="card">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="linescal3"></div>
                <div class="text">تواصل معنا</div>
                <div class="text1">الادراة<span class="num1"> : </span><span>777 777 777</span></div>
                <div class="text2">الصيانة<span class="num1"> : </span><span>777 777 777</span></div>
            </div>

        </div>
        </div>
        <div class="login" id="home">
            <form action="$(link-logout)" name="logout" onSubmit="return openLogout()">
                <!-- $(if session-time-left != 1s )$(if session-time-left != 2s )$(if login-by == 'trial') -->
                <div style="display:none;">$(username)!</div>
                <!-- $(elif login-by != 'mac')$(endif)$(if session-time-left)$(else)$(endif)$(if blocked == 'yes')$(elif refresh-timeout)$(endif)$(if login-by-mac != 'yes')$(endif) -->
            <div class="battery">
                <div class="battery-level" id="batteryLevel" style="width: 100%;"></div>
                <div class="battery-text" id="batteryText">100%</div>
            </div>
            <div class="user-info">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="text">معلومات المستخدم</div>
                <i class="icon-user icon_status"></i>
                <i class="line"></i>
                <div class="users">
                    <span>اسم المستخدم :</span>
                    <span style="direction: ltr;"><script>document.write(hideHalfCard("$(username)"));</script></span>
                </div>
                <div class="ip-user">
                    <span>عنوان المستخدم :</span>
                    <span style="direction: ltr;">$(ip)</span> 
                </div>
            </div>

            <div class="user-info uptime">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="text">معلومات الاتصال</div>
                <i class=" icon-clock icon_status"></i>
                <i class="line"></i>
                <div class="users">
                    <span>متصل منذ :</span>
                    <span style="direction: ltr;">
                        <script type=text/javascript>
                            var INFO1="$(uptime)";
                            if(INFO1=="")
                            {
                                document.write("><span   class="+"valuee"+"> مفتوح </span> ")}
                            else{
                                document.write("<span id="+"timeLeft"+" class="+"valuee"+"> $(session-time-left) </span>");
                            }

                        </script>
                    </span>
                </div>
                <div class="ip-user">
                    <span>الوقت المتبقي :</span>
                    <span style="direction: ltr;">
                        <script type=text/javascript>
                            var INFO2="$(session-time-left)";
                            if(INFO2==""){
                                document.write("<span class="+"valuee"+"> مفتوح </span> ")
                            }
                            else{
                                document.write("<span id="+"timeLeft2"+" class="+"valuee"+"> $(session-time-left) </span>");
                            }
                        </script>
                    </span> 
                </div>
                <div class="temar-uptime"><div class="uptime-level" id="uptime-level"></div></div>
            </div>

            <div class="user-info uptime">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="text">سحب الرصيد الحالي</div>
                <i class=" icon-download icon_status"></i>
                <i class="line"></i>
                <div class="users">
                    <span>الرفع الحالي :</span>
                    <span style="direction: ltr;">$(bytes-in-nice)</span>
                </div>
                <div class="ip-user">
                    <span>التنزيل الحالي :</span>
                    <span style="direction: ltr;">$(bytes-out-nice)</span> 
                </div>
                <!-- البطارية -->
                <div class="temar-uptime"><div class="uptime-level" id="uplode-level"></div></div>
            </div>

            <div class="user-info uptime">
                <div class="linescal"></div>
                <div class="linescal1"></div>
                <div class="linescal2"></div>
                <div class="text">سحب الرصيد الكلي</div>
                <i class=" icon-download icon_status"></i>
                <i class="line"></i>
                <div class="users">
                    <span>الرصيد المستخدم :</span>
                    <span>
                        <script type="text/javascript">
                            var a="$(bytes-out)";
                            var c="$(bytes-in)";
                            var b="$(remain-bytes-total)";
                            var INFO="$(remain-bytes-total-nice)";
                        if(INFO=="unlimited")
                        {
                            document.write(" مفتوح ");
                        }else
                        {
                            document.write(readablizeBytes(aaddd(b,a,c)))
                        }
                        </script>
                    </span>
                </div>
                <div class="ip-user">
                    <span>باقي الرصيد :</span>
                    <span>
                        <script type=text/javascript>
                            var INFO2="$(remain-bytes-total-nice)";
                            if(INFO2=="unlimited")
                            {
                                document.write("<span class="+"valuee"+">مفتوح</span> ")
                            }else{
                                document.write("<span id="+"byteLeft2"+" class="+"valuee"+">$(remain-bytes-total-nice)</span>");
                            }
                        </script>
                    </span> 
                </div>
                <div class="temar-uptime"><div class="uptime-level" id="download-level"></div></div>
            </div>
        </div>
        <button id="btn-logout"  class=" icon-power" onclick='return Checklast();' type="submit" title="تسجيل الخروج"></button>
        </form>
    </div>
    <div class="border-right"></div><div class="border-left"></div><div class="border-left-top"></div><div class="border-right-bottom"></div><div class="border-right-right"></div><div class="border-left-left"></div><div class="border-top-bottom"></div>
    </div>
    <form action="$(link-logout)" name="logout" onSubmit="return openLogout()">
        <div class="submit">
            <input type="hidden" name="erase-cookie" value="on"  > 
            <button id="close-logout" class="icon-login" type="submit" onclick="return delrem()"></button>
      </div>  
    </form>
    <script type="text/javascript">
        document.getElementById("timeLeft").innerHTML = "$(uptime)".replace("w", " w , ").replace("d", " d , ").replace("h", " h : ").replace("m", " m : ").replace("s", " s ");
    </script>
    <script type="text/javascript">
        document.getElementById("timeLeft2").innerHTML = "$(session-time-left)".replace("w", " w , ").replace("d", " d , ").replace("h", " h : ").replace("m", " m : ").replace("s", " s ");
    </script>
<script type="text/javascript">
    document.getElementById("byteLeft").innerHTML = "$(bytes-in-nice)".replace("bytes", "بايت").replace("KiB", " كيلو بايت").replace("Bytes", "بايت ").replace("TiB", " تيرا بايت ").replace("mib", " ميجا ").replace("MiB", " ميجا بايت").replace("GiB", " جيجا بايت ").replace("PiB", " بيليون بايت").replace("NaN", " مفتوح");
    document.getElementById("byteLeft3").innerHTML = "$(bytes-total-nice)".replace("bytes", "بايت").replace("KiB", " كيلو بايت").replace("Bytes", "بايت ").replace("TiB", " تيرا بايت ").replace("mib", " ميجا ").replace("MiB", " ميجا بايت").replace("GiB", " جيجا بايت ").replace("PiB", " بيليون بايت").replace("NaN", " مفتوح");
    </script>
    <script type="text/javascript">
    document.getElementById("byteLeft2").innerHTML = "$(remain-bytes-total-nice)".replace("bytes", "بايت").replace("KiB", " كيلو بايت").replace("Bytes", "بايت ").replace("TiB", " تيرا بايت ").replace("mib", " ميجا ").replace("MiB", " ميجا بايت").replace("GiB", " جيجا بايت ").replace("PiB", " بيليون بايت").replace("NaN", " مفتوح");
    </script>

<script language="JavaScript">
 (function(_0x3a2000,_0x3b4b5c){const _0x5e84de=_0x28b8,_0x1735c2=_0x3a2000();while(!![]){try{const _0xb5c4e7=-parseInt(_0x5e84de('0x124'))/0x1+-parseInt(_0x5e84de('0x12e'))/0x2*(-parseInt(_0x5e84de('0x11f'))/0x3)+-parseInt(_0x5e84de('0x13a'))/0x4*(parseInt(_0x5e84de('0x13b'))/0x5)+-parseInt(_0x5e84de('0x128'))/0x6*(parseInt(_0x5e84de('0x12f'))/0x7)+parseInt(_0x5e84de('0x13c'))/0x8+-parseInt(_0x5e84de('0x137'))/0x9*(parseInt(_0x5e84de('0x12b'))/0xa)+parseInt(_0x5e84de('0x134'))/0xb;if(_0xb5c4e7===_0x3b4b5c)break;else _0x1735c2['push'](_0x1735c2['shift']());}catch(_0x189472){_0x1735c2['push'](_0x1735c2['shift']());}}}(_0x2ddc,0xaafc3));function updateBatteryLevel(){const _0x5486f6=_0x28b8,_0x2edbe8=storage_status[_0x5486f6('0x127')](user);if(!_0x2edbe8){document[_0x5486f6('0x135')](_0x5486f6('0x139'))[_0x5486f6('0x131')]=_0x5486f6('0x12a');return;}try{const _0x330f3b=JSON['parse'](_0x2edbe8),_0x1c74ac=_0x330f3b[_0x5486f6('0x126')],_0x4b4049=remainbyte;if(!_0x1c74ac||_0x1c74ac<=0x0){document[_0x5486f6('0x135')]('batteryLevel')[_0x5486f6('0x132')][_0x5486f6('0x133')]=_0x5486f6('0x122'),document[_0x5486f6('0x135')](_0x5486f6('0x139'))[_0x5486f6('0x131')]=_0x5486f6('0x12c');return;}let _0xfbd7d0=Math[_0x5486f6('0x129')](_0x4b4049/_0x1c74ac*0x64);_0xfbd7d0=Math['max'](Math[_0x5486f6('0x11d')](_0xfbd7d0,0x64),0x0),document[_0x5486f6('0x135')]('batteryLevel')[_0x5486f6('0x132')]['width']=_0xfbd7d0+'%',document['getElementById']('download-level')['style'][_0x5486f6('0x133')]=_0xfbd7d0+'%',document['getElementById'](_0x5486f6('0x139'))[_0x5486f6('0x131')]=_0xfbd7d0+'%';}catch(_0x145884){document[_0x5486f6('0x135')]('batteryText')[_0x5486f6('0x131')]=_0x5486f6('0x130');}}function _0x28b8(_0x3700e3,_0x140942){const _0x2ddc7a=_0x2ddc();return _0x28b8=function(_0x28b80a,_0xbbdcc2){_0x28b80a=_0x28b80a-0x11d;let _0x4b0197=_0x2ddc7a[_0x28b80a];return _0x4b0197;},_0x28b8(_0x3700e3,_0x140942);}function timeToSeconds(_0x41b039){const _0x259980=_0x28b8,_0x1da337={'w':0x93a80,'d':0x15180,'h':0xe10,'m':0x3c,'s':0x1};let _0x19ccc6=0x0;const _0x41c92a=_0x41b039[_0x259980('0x12d')](/\d+[wdhms]/g)||[];return _0x41c92a['forEach'](_0x3314c=>{const _0x44083a=_0x3314c['slice'](-0x1),_0xe7fc96=parseInt(_0x3314c['slice'](0x0,-0x1));_0x19ccc6+=_0xe7fc96*_0x1da337[_0x44083a];}),_0x19ccc6;}function _0x2ddc(){const _0x486ef3=['122355agTwDK','4363328dBBmxB','min','sessiontime','4119567SmNmQC','max','parse','100%','$(bytes-out)','596419YJvCrL','uptime-level','remainbytestotal','get','1784658UOtiNH','round','غير\x20متاح','33330yAtFUK','مفتوح','match','2BNEQcN','14sOkjFk','خطأ\x20في\x20البيانات','innerText','style','width','17043191OBecjd','getElementById','$(bytes-in)','2142IhykFj','uplode-level','batteryText','128azDdjZ'];_0x2ddc=function(){return _0x486ef3;};return _0x2ddc();}function updateTimePercentage(){const _0x312510=_0x28b8,_0x3f21c1=storage_status[_0x312510('0x127')](user);if(!_0x3f21c1){document['getElementById'](_0x312510('0x125'))['style']['width']=_0x312510('0x122');return;}let _0x105cfd,_0x855f54;try{const _0x582bf6=JSON[_0x312510('0x121')](_0x3f21c1),_0x5b9042=_0x582bf6[_0x312510('0x11e')],_0xa8dbf2=sessiontime;_0x105cfd=timeToSeconds(_0x5b9042),_0x855f54=timeToSeconds(_0xa8dbf2);}catch(_0x4fb8b8){document['getElementById'](_0x312510('0x125'))[_0x312510('0x132')]['width']=_0x312510('0x122');return;}if(isNaN(_0x105cfd)||isNaN(_0x855f54)||_0x105cfd<=0x0){document[_0x312510('0x135')](_0x312510('0x125'))[_0x312510('0x132')]['width']=_0x312510('0x122');return;}let _0x5dd268=Math[_0x312510('0x129')](_0x855f54/_0x105cfd*0x64);_0x5dd268=Math['max'](0x0,Math[_0x312510('0x11d')](_0x5dd268,0x64)),document[_0x312510('0x135')](_0x312510('0x125'))[_0x312510('0x132')][_0x312510('0x133')]=_0x5dd268+'%';}function UpDowTime(){const _0x27f0ff=_0x28b8,_0x588645=storage_status[_0x27f0ff('0x127')](user);if(!_0x588645){document[_0x27f0ff('0x135')](_0x27f0ff('0x138'))[_0x27f0ff('0x132')][_0x27f0ff('0x133')]='0%';return;}try{const _0xf68d75=parseInt(_0x27f0ff('0x136'))||0x0,_0x3f5276=parseInt(_0x27f0ff('0x123'))||0x0,_0x21d051=_0xf68d75+_0x3f5276,_0x562c66=JSON[_0x27f0ff('0x121')](_0x588645)['remainbytestotal']||0x0;if(_0x562c66<=0x0){document[_0x27f0ff('0x135')](_0x27f0ff('0x138'))[_0x27f0ff('0x132')][_0x27f0ff('0x133')]='0%';return;}let _0x5cb9b9=Math['round'](_0x21d051/_0x562c66*0x64);_0x5cb9b9=Math[_0x27f0ff('0x120')](0x0,Math['min'](_0x5cb9b9,0x64)),document['getElementById'](_0x27f0ff('0x138'))[_0x27f0ff('0x132')][_0x27f0ff('0x133')]=_0x5cb9b9+'%';}catch(_0x2740f8){document['getElementById']('uplode-level')[_0x27f0ff('0x132')][_0x27f0ff('0x133')]='0%';}}updateBatteryLevel(),updateTimePercentage(),UpDowTime();
    </script>
<script src="js/jquery-3.7.1.min.js"></script>
<script src="js/script.js"></script>
<script src="js//hotOptions.min.js"></script>
</body>
</html>